import type { Theme } from '@react-navigation/native';
import { DarkTheme, DefaultTheme } from '@react-navigation/native';
import type { ClassValue } from 'clsx';
import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export const cn = (...inputs: ClassValue[]) => {
  return twMerge(clsx(inputs));
};

export const THEME = {
  light: {
    background: '#ffffff',
    foreground: '#333333',
    card: '#ffffff',
    cardForeground: '#333333',
    popover: '#ffffff',
    popoverForeground: '#333333',
    primary: '#3b82f6',
    primaryForeground: '#ffffff',
    secondary: '#f3f4f6',
    secondaryForeground: '#4b5563',
    muted: '#f9fafb',
    mutedForeground: '#6b7280',
    accent: '#e0f2fe',
    accentForeground: '#1e3a8a',
    destructive: '#ef4444',
    border: '#e5e7eb',
    input: '#e5e7eb',
    ring: '#3b82f6',
    radius: '0.375rem',
    chart1: '#3b82f6',
    chart2: '#2563eb',
    chart3: '#60a5fa',
    chart4: '#0ea5e9',
    chart5: '#6b7280',
  },
  dark: {
    background: '#171717',
    foreground: '#e5e5e5',
    card: '#262626',
    cardForeground: '#e5e5e5',
    popover: '#262626',
    popoverForeground: '#e5e5e5',
    primary: '#3b82f6',
    primaryForeground: '#ffffff',
    secondary: '#262626',
    secondaryForeground: '#e5e5e5',
    muted: '#262626',
    mutedForeground: '#a3a3a3',
    accent: '#1e3a8a',
    accentForeground: '#bfdbfe',
    destructive: '#ef4444',
    border: '#404040',
    input: '#404040',
    ring: '#3b82f6',
    radius: '0.375rem',
    chart1: '#60a5fa',
    chart2: '#3b82f6',
    chart3: '#93c5fd',
    chart4: '#38bdf8',
    chart5: '#9ca3af',
  },
};

export const NAV_THEME: Record<'light' | 'dark', Theme> = {
  light: {
    ...DefaultTheme,
    colors: {
      background: THEME.light.background,
      border: THEME.light.border,
      card: THEME.light.card,
      notification: THEME.light.destructive,
      primary: THEME.light.primary,
      text: THEME.light.foreground,
    },
  },
  dark: {
    ...DarkTheme,
    colors: {
      background: THEME.dark.background,
      border: THEME.dark.border,
      card: THEME.dark.card,
      notification: THEME.dark.destructive,
      primary: THEME.dark.primary,
      text: THEME.dark.foreground,
    },
  },
};
