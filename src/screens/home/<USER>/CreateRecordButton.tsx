import { THEME } from '@/utils/theme';
import { Plus } from 'lucide-react-native';
import { TouchableOpacity } from 'react-native';

type CreateRecordButtonProps = {};

/**
 * @description 创建一条记录
 * <AUTHOR>
 * @date 2025-08-28
 */
export const CreateRecordButton = (props: CreateRecordButtonProps) => {
  // const {} = props;

  return (
    <TouchableOpacity className="absolute bottom-0 right-8 rounded-xl bg-primary p-4">
      <Plus color={THEME.light.primaryForeground} />
    </TouchableOpacity>
  );
};
