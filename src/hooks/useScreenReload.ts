import { NavigationProps, ScreenReloadParams } from '@/navigation/types';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { useCallback } from 'react';

type Params<RecordType extends ScreenReloadParams> = {
  pageParams: RecordType | undefined;
  reload: () => Promise<void>;
};

/** 页面刷新逻辑 */
export const useScreenReload = <RecordType extends ScreenReloadParams>(
  p: Params<RecordType>,
) => {
  const { pageParams, reload } = p;

  const navigation = useNavigation<NavigationProps<any>>();

  const onReload = useCallback(() => {
    if (pageParams?.reload !== false) {
      reload();
      navigation.setParams({ reload: false });
    }
    /* eslint-disable-next-line react-hooks/exhaustive-deps */
  }, [pageParams?.reload]);

  useFocusEffect(onReload);
};
