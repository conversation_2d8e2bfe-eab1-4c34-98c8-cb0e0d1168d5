import { CommonRecord } from '@/types/common';
import { NavigationProps, RouterMap } from '@/navigation/types';
import {
  CommonActions,
  NavigationState,
  useNavigation,
} from '@react-navigation/native';
import { useCallback } from 'react';

/** 通用导航逻辑 */
export const useNavigationActions = () => {
  const navigation = useNavigation<NavigationProps<any>>();

  const findRouteKey = useCallback(
    (
      routeName: RouterMap,
      state: Readonly<NavigationState> | undefined,
    ): string | null => {
      if (!state?.routes) {
        return null;
      }

      for (let route of state.routes) {
        if (route.name === routeName && route.key) {
          return route.key;
        }

        if (route.state) {
          const nestedRouteKey = findRouteKey(
            routeName,
            route.state as NavigationState,
          );
          if (nestedRouteKey) {
            return nestedRouteKey;
          }
        }
      }

      return null;
    },
    [],
  );

  const setScreenParams = useCallback(
    (routeName: RouterMap, params: CommonRecord) => {
      const state = navigation.getState();
      const routeKey = findRouteKey(routeName, state);

      if (routeKey) {
        navigation.dispatch({
          ...CommonActions.setParams(params),
          source: routeKey,
        });
      } else {
        console.warn('royrao: ', 'Page not found');
      }
    },
    [findRouteKey, navigation],
  );

  /** 将指定页面设为需要刷新 */
  const setNeedsReload = useCallback(
    (routeName: RouterMap) => {
      setScreenParams(routeName, { reload: true });
    },
    [setScreenParams],
  );

  return { setNeedsReload, setScreenParams, findRouteKey };
};
