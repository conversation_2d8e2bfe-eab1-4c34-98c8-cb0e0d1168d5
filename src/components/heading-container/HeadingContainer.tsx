import { cn } from '@/utils/theme';
import type { PropsWithChildren } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Text } from '../reuseables/Text';

type HeadingContainerProps = {
  className?: string;
  header: string;
};

/**
 * @description 带大标题的容器
 * <AUTHOR>
 * @date 2025-08-28
 */
export const HeadingContainer = (
  props: PropsWithChildren<HeadingContainerProps>,
) => {
  const { children, className, header } = props;

  return (
    <SafeAreaView
      className={cn(
        'flex flex-1 flex-col items-start justify-start px-5',
        className,
      )}
    >
      <Text className="text-4xl font-semibold">{header}</Text>

      {children}
    </SafeAreaView>
  );
};
