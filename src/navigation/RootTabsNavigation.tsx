import { CapsuleTabBar } from '@/components';
import { HomeScreen, ProfileScreen } from '@/screens';
import type { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStaticNavigation } from '@react-navigation/native';

const renderTabBar = (props: BottomTabBarProps) => {
  return <CapsuleTabBar {...props} />;
};

const RootTabs = createBottomTabNavigator({
  tabBar: renderTabBar,
  screenOptions: {
    headerShown: false,
  },
  screens: {
    Home: {
      screen: HomeScreen,
      options: { tabBarLabel: '首页' },
    },
    Profile: {
      screen: ProfileScreen,
      options: { tabBarLabel: '我的' },
    },
  },
});

export const RootTabsNavigation = createStaticNavigation(RootTabs);
