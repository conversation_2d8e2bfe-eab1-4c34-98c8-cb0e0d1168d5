import type { NavigationProp, RouteProp } from '@react-navigation/native';

export enum RouterMap {
  /** Tab Screens */
  Home = 'Home',
  Profile = 'Profile',

  /** Future Screens - 保留用于后续扩展 */
  GrudgeCreate = 'stack_grudgeCreate',
  HistoryList = 'stack_historyList',
  GrudgeList = 'tab_grudgeList',
  Award = 'tab_award',
}

export type RouteParamList = {
  [RouterMap.Home]?: undefined;
  [RouterMap.Profile]?: undefined;
  [RouterMap.HistoryList]?: ScreenReloadParams;
  [RouterMap.GrudgeList]?: ScreenReloadParams;
  [RouterMap.Award]?: undefined;
  [RouterMap.GrudgeCreate]?: undefined;
};

export type NavigationProps<R extends RouterMap> = NavigationProp<
  RouteParamList,
  R
>;

export type RouteProps<R extends RouterMap> = RouteProp<RouteParamList, R>;

export type ScreenReloadParams = {
  reload?: boolean;
};
