const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const path = require('path');
const { withNativeWind } = require('nativewind/metro');
const {
  wrapWithReanimatedMetroConfig,
} = require('react-native-reanimated/metro-config');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {
  resolver: {
    unstable_enableSymlinks: true,
  },
  watchFolders: [path.resolve(__dirname, './node_modules/.pnpm')],
};

const commonConfig = mergeConfig(getDefaultConfig(__dirname), config);

const nativeWindConfig = withNativeWind(commonConfig, {
  input: './src/styles/global.css',
  inlineRem: 16,
});

module.exports = wrapWithReanimatedMetroConfig(nativeWindConfig);
